// app/api/namecheap/route.ts
import crypto from 'crypto';

interface NamecheapConfig {
  apiUser: string;
  apiKey: string;
  username: string;
  clientIp: string;
  sandbox: boolean;
}

interface DomainCheckResult {
  Domain: string;
  Available: boolean;
  IsPremiumName: boolean;
  Price?: number;
  Error?: string;
}

const getNamecheapConfig = (): NamecheapConfig => ({
  apiUser: process.env.NAMECHEAP_API_USER || '',
  apiKey: process.env.NAMECHEAP_API_KEY || '',
  username: process.env.NAMECHEAP_USERNAME || '',
  clientIp: process.env.NAMECHEAP_CLIENT_IP || '',
  sandbox: process.env.NODE_ENV !== 'production',
});

const getNamecheapBaseUrl = (sandbox: boolean): string => {
  return sandbox 
    ? 'https://api.sandbox.namecheap.com/xml.response'
    : 'https://api.namecheap.com/xml.response';
};

const buildNamecheapUrl = (command: string, params: Record<string, string>, config: NamecheapConfig): string => {
  const baseUrl = getNamecheapBaseUrl(config.sandbox);
  const baseParams = {
    ApiUser: config.apiUser,
    ApiKey: config.apiKey,
    UserName: config.username,
    Command: command,
    ClientIp: config.clientIp,
    ...params
  };

  const queryString = Object.entries(baseParams)
    .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
    .join('&');

  return `${baseUrl}?${queryString}`;
};

const parseXmlResponse = async (xmlText: string): Promise<any> => {
  // Simple regex-based XML parser for server-side use
  const extractValue = (xml: string, tag: string): string => {
    const regex = new RegExp(`<${tag}[^>]*>([^<]*)</${tag}>`, 'i');
    const match = xml.match(regex);
    return match ? match[1].trim() : '';
  };

  const extractAttribute = (xml: string, tag: string, attr: string): string => {
    const regex = new RegExp(`<${tag}[^>]*${attr}="([^"]*)"`, 'i');
    const match = xml.match(regex);
    return match ? match[1] : '';
  };

  // Check if the response is successful
  const status = extractAttribute(xmlText, 'ApiResponse', 'Status');
  
  if (status !== 'OK') {
    const error = extractValue(xmlText, 'Error');
    throw new Error(error || 'Unknown API error');
  }

  // Parse domain check results
  const domainCheckResults = [];
  const domainRegex = /<DomainCheckResult[^>]*>/gi;
  let match;
  
  while ((match = domainRegex.exec(xmlText)) !== null) {
    const domainTag = match[0];
    const domain = extractAttribute(domainTag, 'DomainCheckResult', 'Domain');
    const available = extractAttribute(domainTag, 'DomainCheckResult', 'Available');
    const isPremium = extractAttribute(domainTag, 'DomainCheckResult', 'IsPremiumName');
    const premiumPrice = extractAttribute(domainTag, 'DomainCheckResult', 'PremiumRegistrationPrice');
    const regularPrice = extractAttribute(domainTag, 'DomainCheckResult', 'RegularPrice');
    
    domainCheckResults.push({
      Domain: domain,
      Available: available,
      IsPremiumName: isPremium,
      PremiumRegistrationPrice: premiumPrice,
      RegularPrice: regularPrice
    });
  }

  return {
    ApiResponse: {
      Status: status,
      CommandResponse: {
        DomainCheckResult: domainCheckResults
      }
    }
  };
};

export const checkDomainAvailability = async (domains: string[]): Promise<DomainCheckResult[]> => {
  const config = getNamecheapConfig();
  
  if (!config.apiUser || !config.apiKey || !config.username || !config.clientIp) {
    throw new Error('Missing required Namecheap API configuration');
  }

  const domainList = domains.join(',');
  const url = buildNamecheapUrl('namecheap.domains.check', {
    DomainList: domainList
  }, config);

  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const xmlText = await response.text();
    const jsonResponse = await parseXmlResponse(xmlText);

    const apiResponse = jsonResponse.ApiResponse || jsonResponse;
    
    if (apiResponse.Status !== 'OK') {
      const errorMsg = apiResponse.Errors?.Error || 'Unknown API error';
      throw new Error(errorMsg);
    }

    const domainCheckResults = apiResponse.CommandResponse?.DomainCheckResult || [];
    const results = Array.isArray(domainCheckResults) ? domainCheckResults : [domainCheckResults];

    return results.map((result: any) => ({
      Domain: result.Domain,
      Available: result.Available === 'true',
      IsPremiumName: result.IsPremiumName === 'true',
      Price: result.IsPremiumName === 'true' 
        ? parseFloat(result.PremiumRegistrationPrice || '0') 
        : parseFloat(result.RegularPrice || '0'),
    }));

  } catch (error) {
    console.error('Namecheap API error:', error);
    throw new Error(error instanceof Error ? error.message : 'Failed to check domain availability');
  }
};

export const registerDomain = async (domain: string, years: number = 1): Promise<any> => {
  const config = getNamecheapConfig();
  
  if (!config.apiUser || !config.apiKey || !config.username || !config.clientIp) {
    throw new Error('Missing required Namecheap API configuration');
  }

  const url = buildNamecheapUrl('namecheap.domains.create', {
    DomainName: domain,
    Years: years.toString(),
    // Add default contact information - you should collect this from your form
    AuxBillingFirstName: 'John',
    AuxBillingLastName: 'Doe',
    AuxBillingAddress1: '123 Main St',
    AuxBillingCity: 'Anytown',
    AuxBillingStateProvince: 'ST',
    AuxBillingPostalCode: '12345',
    AuxBillingCountry: 'US',
    AuxBillingPhone: '*************',
    AuxBillingEmailAddress: '<EMAIL>',
    // Technical contact (can be same as billing)
    TechFirstName: 'John',
    TechLastName: 'Doe',
    TechAddress1: '123 Main St',
    TechCity: 'Anytown',
    TechStateProvince: 'ST',
    TechPostalCode: '12345',
    TechCountry: 'US',
    TechPhone: '*************',
    TechEmailAddress: '<EMAIL>',
    // Admin contact (can be same as billing)
    AdminFirstName: 'John',
    AdminLastName: 'Doe',
    AdminAddress1: '123 Main St',
    AdminCity: 'Anytown',
    AdminStateProvince: 'ST',
    AdminPostalCode: '12345',
    AdminCountry: 'US',
    AdminPhone: '*************',
    AdminEmailAddress: '<EMAIL>',
    // Registrant contact (can be same as billing)
    RegistrantFirstName: 'John',
    RegistrantLastName: 'Doe',
    RegistrantAddress1: '123 Main St',
    RegistrantCity: 'Anytown',
    RegistrantStateProvince: 'ST',
    RegistrantPostalCode: '12345',
    RegistrantCountry: 'US',
    RegistrantPhone: '*************',
    RegistrantEmailAddress: '<EMAIL>',
  }, config);

  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const xmlText = await response.text();
    const jsonResponse = await parseXmlResponse(xmlText);

    const apiResponse = jsonResponse.ApiResponse || jsonResponse;
    
    if (apiResponse.Status !== 'OK') {
      const errorMsg = apiResponse.Errors?.Error || 'Unknown API error';
      throw new Error(errorMsg);
    }

    return apiResponse.CommandResponse?.DomainCreateResult;

  } catch (error) {
    console.error('Namecheap registration error:', error);
    throw new Error(error instanceof Error ? error.message : 'Failed to register domain');
  }
};

// App Router POST handler
export async function POST(request: Request) {
  try {
    const { domain, action = 'check' } = await request.json();

    if (!domain) {
      return Response.json({ error: 'Domain name is required' }, { status: 400 });
    }

    if (action === 'check') {
      const baseDomain = domain.replace(/\.(com|net|org|io|co|app|dev)$/i, '');
      const tlds = ['com', 'net', 'org', 'io', 'co', 'app', 'dev'];
      const domainsToCheck = tlds.map(tld => `${baseDomain}.${tld}`);

      const results = await checkDomainAvailability(domainsToCheck);
      return Response.json({ results });
    } else if (action === 'register') {
      const result = await registerDomain(domain);
      return Response.json({ result });
    } else {
      return Response.json({ error: 'Invalid action' }, { status: 400 });
    }

  } catch (error) {
    console.error('API error:', error);
    const errorMessage = error instanceof Error ? error.message : 'Internal server error';
    return Response.json({ error: errorMessage }, { status: 500 });
  }
}