import { NextRequest, NextResponse } from 'next/server';
import Namecheap from 'node-namecheap';

const namecheap = new Namecheap({
  ApiUser: process.env.NAMECHEAP_API_USER || '',
  ApiKey: process.env.NAMECHEAP_API_KEY || '',
  UserName: process.env.NAMECHEAP_API_USER || '',
  ClientIp: process.env.NAMECHEAP_CLIENT_IP || '',
  sandbox: true, // Set to false for production
});

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const domain = searchParams.get('domain');
    if (!domain) {
      return NextResponse.json({ error: 'Domain is required.' }, { status: 400 });
    }
    const parts = domain.split('.');
    const sld = parts.slice(0, -1).join('.');
    const tld = parts.slice(-1)[0];
    if (!sld || !tld) {
      return NextResponse.json({ error: 'Invalid domain format.' }, { status: 400 });
    }
    const result = await namecheap.domains.dns.getHosts(sld, tld);
    return NextResponse.json({ success: true, records: result });
  } catch (error) {
    const err = error as Error;
    console.error('[NAMECHEAP DNS GET ERROR]', err);
    return NextResponse.json({ error: err.message || 'An unknown error occurred.' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const { domain, records } = await request.json();
    if (!domain || !Array.isArray(records)) {
      return NextResponse.json({ error: 'Domain and records are required.' }, { status: 400 });
    }
    const parts = domain.split('.');
    const sld = parts.slice(0, -1).join('.');
    const tld = parts.slice(-1)[0];
    if (!sld || !tld) {
      return NextResponse.json({ error: 'Invalid domain format.' }, { status: 400 });
    }
    // records should be an array of host objects as required by Namecheap
    const result = await namecheap.domains.dns.setHosts(sld, tld, records);
    return NextResponse.json({ success: true, result });
  } catch (error) {
    const err = error as Error;
    console.error('[NAMECHEAP DNS SET ERROR]', err);
    return NextResponse.json({ error: err.message || 'An unknown error occurred.' }, { status: 500 });
  }
} 