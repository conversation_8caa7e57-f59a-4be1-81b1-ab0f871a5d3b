import { NextRequest, NextResponse } from 'next/server';
import Namecheap from 'node-namecheap';

const namecheap = new Namecheap({
  ApiUser: process.env.NAMECHEAP_API_USER || '',
  ApiKey: process.env.NAMECHEAP_API_KEY || '',
  UserName: process.env.NAMECHEAP_API_USER || '',
  ClientIp: process.env.NAMECHEAP_CLIENT_IP || '',
  sandbox: true, // Set to false for production
});

export async function POST(request: NextRequest) {
  try {
    const { domain, years = 1 } = await request.json();
    if (!domain || typeof domain !== 'string') {
      return NextResponse.json({ error: 'Domain name is required.' }, { status: 400 });
    }
    const parts = domain.split('.');
    const sld = parts.slice(0, -1).join('.');
    const tld = parts.slice(-1)[0];
    if (!sld || !tld) {
      return NextResponse.json({ error: 'Invalid domain format. Please include TLD (e.g., .com).' }, { status: 400 });
    }
    // Register the domain
    const result = await namecheap.domains.create({
      DomainName: domain,
      Years: years,
      // You may need to provide additional required fields (Registrant info, etc.)
      // For demo, we use minimal fields. In production, collect full registrant info.
      RegistrantFirstName: 'John',
      RegistrantLastName: 'Doe',
      RegistrantAddress1: '123 Main St',
      RegistrantCity: 'Los Angeles',
      RegistrantStateProvince: 'CA',
      RegistrantPostalCode: '90001',
      RegistrantCountry: 'US',
      RegistrantPhone: '*************',
      RegistrantEmailAddress: '<EMAIL>',
    });
    return NextResponse.json({ success: true, result });
  } catch (error) {
    const err = error as Error;
    console.error('[NAMECHEAP REGISTER ERROR]', err);
    return NextResponse.json({ error: err.message || 'An unknown error occurred.' }, { status: 500 });
  }
} 