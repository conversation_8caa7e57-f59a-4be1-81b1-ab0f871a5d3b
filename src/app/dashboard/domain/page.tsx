"use client";

import { useState } from 'react';

interface DomainCheckResult {
  Domain: string;
  Available: boolean;
  IsPremiumName: boolean;
  Price?: number;
  Error?: string;
}

const DomainPage = () => {
  const [domainName, setDomainName] = useState('');
  const [results, setResults] = useState<DomainCheckResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [registering, setRegistering] = useState<string | null>(null);

  const handleSearch = async (e: React.MouseEvent | React.KeyboardEvent) => {
    e.preventDefault();
    if (!domainName) return;

    setLoading(true);
    setResults([]);

    try {
      const response = await fetch('/api/namecheap', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ domain: domainName, action: 'check' }),
      });

      if (!response.ok) {
        const { error } = await response.json();
        throw new Error(error || 'Failed to check domain availability.');
      }

      const data = await response.json();
      setResults(data.results);
    } catch (error) {
      const err = error as Error;
      console.error('Domain search error:', err);
      setResults([{ Domain: domainName, Available: false, IsPremiumName: false, Error: err.message }]);
    } finally {
      setLoading(false);
    }
  };

  const handleRegister = async (domain: string) => {
    setRegistering(domain);
    
    try {
      const response = await fetch('/api/namecheap', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ domain, action: 'register' }),
      });

      if (!response.ok) {
        const { error } = await response.json();
        throw new Error(error || 'Failed to register domain.');
      }

      const data = await response.json();
      
      // Update the results to show the domain as registered
      setResults(prev => prev.map(result => 
        result.Domain === domain 
          ? { ...result, Available: false, Error: undefined }
          : result
      ));

      alert(`Domain ${domain} has been successfully registered!`);
    } catch (error) {
      const err = error as Error;
      console.error('Domain registration error:', err);
      alert(`Failed to register ${domain}: ${err.message}`);
    } finally {
      setRegistering(null);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-4xl font-bold text-gray-800 mb-4">Register a Domain</h1>
        <p className="text-gray-600 mb-8">Find and register the perfect domain for your new website.</p>

        <div className="flex items-center gap-4 mb-8 bg-white p-6 rounded-lg shadow-sm">
          <input
            type="text"
            value={domainName}
            onChange={(e) => setDomainName(e.target.value)}
            placeholder="Find your new domain (e.g., my-awesome-site)"
            className="flex-grow p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 transition"
            onKeyPress={(e) => e.key === 'Enter' && handleSearch(e)}
          />
          <button
            onClick={handleSearch}
            disabled={loading}
            className="bg-green-600 text-white font-bold py-3 px-6 rounded-md hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition"
          >
            {loading ? 'Searching...' : 'Search'}
          </button>
        </div>

        {results.length > 0 && (
          <div className="bg-white p-6 rounded-lg shadow-sm">
            <h2 className="text-2xl font-bold text-gray-800 mb-4">Results</h2>
            <ul className="space-y-4">
              {results.map((result) => (
                <li key={result.Domain} className="flex items-center justify-between p-4 border rounded-md">
                  <div className="flex flex-col">
                    <span className="font-medium text-lg text-gray-700">{result.Domain}</span>
                    {result.Price && (
                      <span className="text-sm text-gray-500">
                        ${result.Price.toFixed(2)}/year
                        {result.IsPremiumName && <span className="text-orange-500 ml-2">Premium</span>}
                      </span>
                    )}
                  </div>
                  {result.Error ? (
                    <span className="text-red-500 font-semibold">{result.Error}</span>
                  ) : result.Available ? (
                    <div className="flex items-center gap-4">
                      <span className="text-green-600 font-bold">Available!</span>
                      <button
                        onClick={() => handleRegister(result.Domain)}
                        disabled={registering === result.Domain}
                        className="bg-blue-600 text-white font-bold py-2 px-4 rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition"
                      >
                        {registering === result.Domain ? 'Registering...' : 'Register'}
                      </button>
                    </div>
                  ) : (
                    <span className="text-red-500 font-semibold">Unavailable</span>
                  )}
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* Information Section */}
        <div className="mt-8 bg-blue-50 p-6 rounded-lg">
          <h3 className="text-xl font-semibold text-blue-800 mb-2">How it works</h3>
          <ul className="text-blue-700 space-y-1">
            <li>• Enter your desired domain name (without the extension)</li>
            <li>• We'll check availability across popular extensions (.com, .net, .org, etc.)</li>
            <li>• Click "Register" to purchase available domains</li>
            <li>• Premium domains may have higher pricing</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default DomainPage;