'use client'
import React, { useEffect, useState } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import MapDomainModal from './MapDomainModal';
import AskDomainModal from './AskDomainModal';
import { useRouter, useSearchParams } from 'next/navigation';

interface Site {
  id: string;
  site_name: string;
  expiry_status: 'Permanent' | 'Temporary';
}

const DashboardPage: React.FC = () => {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [sites, setSites] = useState<Site[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [search, setSearch] = useState<string>("");
  const [isMapDomainOpen, setIsMapDomainOpen] = useState(false);
  const [isAskDomainOpen, setIsAskDomainOpen] = useState(false);
  const [selectedSiteName, setSelectedSiteName] = useState<string>('');
  const [selectedSiteId, setSelectedSiteId] = useState<string>('');
  const supabase = createClientComponentClient();

  useEffect(() => {
    // Trigger AskDomainModal if redirected from checkout
    const postCheckout = searchParams.get('postCheckout');
    const siteIdFromParam = searchParams.get('siteId');
    if (postCheckout && siteIdFromParam) {
      setSelectedSiteId(siteIdFromParam);
      setIsAskDomainOpen(true);
    }
  }, [searchParams]);

  useEffect(() => {
    const fetchSites = async () => {
      try {
        const { data, error } = await supabase
          .from('user-dashboard')
          .select('id, site_name, expiry_status');

        if (error) {
          throw error;
        }

        setSites(data as Site[]);
      } catch (err) {
        console.error('Error fetching sites:', err);
        setError(err instanceof Error ? err.message : 'An unknown error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchSites();
  }, []);

  if (loading) {
    return <div className="py-8 text-center">Loading sites...</div>;
  }

  if (error) {
    return <div className="py-8 text-center text-red-500">Error: {error}</div>;
  }

  return (
    <div className="flex flex-col h-full p-8 bg-gray-100">
      {/* Dashboard Header */}
      <div className="flex items-center justify-between p-6 mb-6 bg-white rounded-lg shadow-md">
        <h1 className="text-2xl font-semibold text-gray-800">Websites</h1>
        <div className="flex items-center space-x-4">
          <div className="relative">
            <input
              type="text"
              placeholder="Search"
              className="py-2 pl-10 pr-4 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={search}
              onChange={e => setSearch(e.target.value)}
            />
            <svg
              className="absolute w-5 h-5 text-gray-400 transform -translate-y-1/2 left-3 top-1/2"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
              ></path>
            </svg>
          </div>
        </div>
      </div>

      {/* Sites Table */}
      <div className="flex-1 overflow-hidden bg-white rounded-lg shadow-md">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th
                scope="col"
                className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase"
              >
                Site Name
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase"
              >
                Actions
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase"
              >
                Expiry
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {(search.trim() ? sites.filter(site => site.site_name.toLowerCase().includes(search.toLowerCase())) : sites).map((site) => (
              <tr key={site.id}>
                <td className="flex items-center px-6 py-4 text-sm font-medium text-gray-900 whitespace-nowrap">
                  {site.site_name}
                  <a href="#" className="ml-2 text-gray-400 hover:text-gray-600">
                    <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                    </svg>
                  </a>
                </td>
                <td className="px-6 py-4 text-sm font-medium text-right whitespace-nowrap">
                  <div className="flex items-center space-x-2">
                    {/* Map Domain Icon - Globe icon, always visible */}
                    <button 
                      className="text-blue-500 hover:text-blue-700"
                      title="Map Domain"
                      onClick={() => { setSelectedSiteName(site.site_name); setIsMapDomainOpen(true); }}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </button>
                    {/* Go Live Icon - Rocket icon, only for Temporary sites */}
                    {site.expiry_status === 'Temporary' && (
                      <button 
                        className="text-green-500 hover:text-green-700"
                        title="Choose Plan"
                        onClick={() => { window.location.href = `/payments?siteId=${site.id}` }}
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                        </svg>
                      </button>
                    )}
                    {/* Change Plan Icon - Credit card icon, only for Permanent sites */}
                    {site.expiry_status === 'Permanent' && (
                      <button
                        className="text-purple-500 hover:text-purple-700"
                        title="Change Plan"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <rect x="2" y="7" width="20" height="10" rx="2" ry="2" stroke="currentColor" strokeWidth="2" fill="none" />
                          <path d="M2 11h20" stroke="currentColor" strokeWidth="2" />
                          <circle cx="7" cy="15" r="1" fill="currentColor" />
                          <circle cx="11" cy="15" r="1" fill="currentColor" />
                        </svg>
                      </button>
                    )}
                  </div>
                </td>
                <td className="px-6 py-4 text-sm whitespace-nowrap">
                  <span
                    className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${site.expiry_status === 'Permanent' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`}
                  >
                    {site.expiry_status}
                  </span>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      {/* Table Footer */}
      <div className="p-4 text-sm text-gray-600 bg-white border-t border-gray-200 rounded-b-lg">
        {sites.length} Sites
      </div>
      <MapDomainModal 
        isOpen={isMapDomainOpen} 
        onClose={() => setIsMapDomainOpen(false)} 
        siteName={selectedSiteName} 
      />

      <AskDomainModal
        isOpen={isAskDomainOpen}
        onYes={() => {
          // open map domain modal
          const site = sites.find(s => s.id === selectedSiteId);
          if (site) {
            setSelectedSiteName(site.site_name);
            setIsMapDomainOpen(true);
          }
          setIsAskDomainOpen(false);
        }}
        onNo={() => {
          router.push(`/dashboard/domain`);
          setIsAskDomainOpen(false);
        }}
      />
    </div>
  );
};

export default DashboardPage;