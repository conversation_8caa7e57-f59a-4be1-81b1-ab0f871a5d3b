"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/AskDomainModal.tsx":
/*!**********************************************!*\
  !*** ./src/app/dashboard/AskDomainModal.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst AskDomainModal = (param)=>{\n    let { isOpen, onYes, onNo } = param;\n    if (!isOpen) return null;\n    // Handler to close modal when clicking outside the modal content\n    const handleBackdropClick = (e)=>{\n        if (e.target === e.currentTarget) {\n            onNo();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-50 flex items-center justify-center bg-gray-600 bg-opacity-50\",\n        onClick: handleBackdropClick,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative w-full max-w-md p-6 bg-white rounded-lg shadow-xl\",\n            onClick: (e)=>e.stopPropagation(),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: \"absolute top-3 right-3 text-gray-400 hover:text-gray-600 text-2xl font-bold focus:outline-none\",\n                    onClick: onNo,\n                    \"aria-label\": \"Close\",\n                    children: \"\\xd7\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/AskDomainModal.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"mb-2 text-xl font-semibold text-gray-800 text-center\",\n                    children: \"Thank you for your purchase!\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/AskDomainModal.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mb-4 text-center text-gray-700\",\n                    children: \"Do you already have a custom domain you’d like to use?\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/AskDomainModal.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-center space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onYes,\n                            className: \"px-4 py-2 text-white bg-green-600 rounded-md hover:bg-green-700\",\n                            children: \"Yes\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/AskDomainModal.tsx\",\n                            lineNumber: 41,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onNo,\n                            className: \"px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300\",\n                            children: \"No\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/AskDomainModal.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/AskDomainModal.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/AskDomainModal.tsx\",\n            lineNumber: 25,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/AskDomainModal.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, undefined);\n};\n_c = AskDomainModal;\n/* harmony default export */ __webpack_exports__[\"default\"] = (AskDomainModal);\nvar _c;\n$RefreshReg$(_c, \"AskDomainModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/AskDomainModal.tsx\n"));

/***/ })

});